import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ratulive/model/livestream/livestream.dart';
import 'package:ratulive/screen/live_stream/livestream_screen/audience/live_stream_audience_screen.dart';
import 'package:ratulive/screen/live_stream/livestream_screen/livestream_screen_controller.dart';
import 'package:ratulive/common/manager/logger.dart';
import 'package:ratulive/utilities/stream_debug_helper.dart';

/// Test widget to debug single stream functionality
/// Use this to isolate video display issues from room management issues
class SingleStreamTest extends StatefulWidget {
  final Livestream livestream;

  const SingleStreamTest({
    super.key,
    required this.livestream,
  });

  @override
  State<SingleStreamTest> createState() => _SingleStreamTestState();
}

class _SingleStreamTestState extends State<SingleStreamTest> {
  bool _isInitializing = false;
  String? _error;

  @override
  void initState() {
    super.initState();
    _initializeController();
  }

  Future<void> _initializeController() async {
    if (_isInitializing) return;
    
    setState(() {
      _isInitializing = true;
      _error = null;
    });

    try {
      final tag = widget.livestream.roomID;
      if (tag == null || tag.isEmpty) {
        throw Exception('Invalid room ID');
      }

      Loggers.info('🧪 TEST: Initializing single stream controller for room: $tag');
      StreamDebugHelper.instance.debugDump('Single stream test start');

      // Clean up any existing controller first
      if (Get.isRegistered<LivestreamScreenController>(tag: tag)) {
        Loggers.warning('🧪 TEST: Controller already exists, cleaning up first');
        final existingController = Get.find<LivestreamScreenController>(tag: tag);
        await existingController.logoutRoom(1);
        Get.delete<LivestreamScreenController>(tag: tag);
        await Future.delayed(const Duration(milliseconds: 500));
      }

      // Create new controller
      Get.put(
        LivestreamScreenController(widget.livestream.obs, false),
        tag: tag,
      );

      Loggers.success('🧪 TEST: Controller created successfully for room: $tag');
      StreamDebugHelper.instance.logControllerInfo(tag);

      setState(() {
        _isInitializing = false;
      });

    } catch (e) {
      Loggers.error('🧪 TEST: Failed to initialize controller: $e');
      setState(() {
        _error = e.toString();
        _isInitializing = false;
      });
    }
  }

  @override
  void dispose() {
    _cleanupController();
    super.dispose();
  }

  Future<void> _cleanupController() async {
    final tag = widget.livestream.roomID;
    if (tag != null && tag.isNotEmpty) {
      try {
        if (Get.isRegistered<LivestreamScreenController>(tag: tag)) {
          final controller = Get.find<LivestreamScreenController>(tag: tag);
          await controller.logoutRoom(1);
          Get.delete<LivestreamScreenController>(tag: tag);
          Loggers.info('🧪 TEST: Cleaned up controller for room: $tag');
        }
      } catch (e) {
        Loggers.error('🧪 TEST: Error cleaning up controller: $e');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        title: Text('Single Stream Test: ${widget.livestream.roomID}'),
        backgroundColor: Colors.purple,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _initializeController,
          ),
          IconButton(
            icon: const Icon(Icons.bug_report),
            onPressed: () {
              StreamDebugHelper.instance.debugDump('Manual debug dump');
            },
          ),
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error, color: Colors.red, size: 64),
            const SizedBox(height: 16),
            Text(
              'Error: $_error',
              style: const TextStyle(color: Colors.white),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _initializeController,
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (_isInitializing) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(color: Colors.purple),
            SizedBox(height: 16),
            Text(
              'Initializing stream...',
              style: TextStyle(color: Colors.white),
            ),
          ],
        ),
      );
    }

    final tag = widget.livestream.roomID;
    if (tag == null || tag.isEmpty) {
      return const Center(
        child: Text(
          'Invalid room ID',
          style: TextStyle(color: Colors.white),
        ),
      );
    }

    // Check if controller exists
    if (!Get.isRegistered<LivestreamScreenController>(tag: tag)) {
      return const Center(
        child: Text(
          'Controller not found',
          style: TextStyle(color: Colors.white),
        ),
      );
    }

    // Show the actual stream
    return LiveStreamAudienceScreen(
      livestream: widget.livestream,
      isHost: false,
      key: ValueKey('test_stream_$tag'),
    );
  }
}
