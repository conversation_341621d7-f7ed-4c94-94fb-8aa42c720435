import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:keyboard_avoider/keyboard_avoider.dart';
import 'package:ratulive/model/livestream/livestream.dart';
import 'package:ratulive/screen/live_stream/livestream_screen/audience/widget/livestream_audience_top_view.dart';
import 'package:ratulive/screen/live_stream/livestream_screen/livestream_screen_controller.dart';
import 'package:ratulive/screen/live_stream/livestream_screen/view/battle_view.dart';
import 'package:ratulive/screen/live_stream/livestream_screen/view/live_stream_bottom_view.dart';
import 'package:ratulive/screen/live_stream/livestream_screen/view/live_video_player.dart';
import 'package:ratulive/screen/live_stream/livestream_screen/view/livestream_view.dart';
import 'package:ratulive/screen/live_stream/livestream_screen/widget/battle_start_countdown_overlay.dart';
import 'package:ratulive/screen/live_stream/livestream_screen/widget/live_stream_background_blur_image.dart';
import 'package:ratulive/utilities/theme_res.dart';

class LiveStreamAudienceScreen extends StatefulWidget {
  final Livestream livestream;
  final bool isHost;

  const LiveStreamAudienceScreen(
      {super.key, required this.livestream, required this.isHost});

  @override
  State<LiveStreamAudienceScreen> createState() => _LiveStreamAudienceScreenState();
}

class _LiveStreamAudienceScreenState extends State<LiveStreamAudienceScreen> {

  @override
  Widget build(BuildContext context) {
    // Get the existing controller that was created by the parent viewer screen
    final tag = widget.livestream.roomID;
    if (tag == null || tag.isEmpty) {
      return Container(
        color: Colors.black,
        child: const Center(
          child: Text(
            'Invalid room ID',
            style: TextStyle(color: Colors.white),
          ),
        ),
      );
    }

    // CRITICAL: Only use existing controller, never create new ones here
    // This prevents the multiple rooms error
    LivestreamScreenController controller;
    try {
      controller = Get.find<LivestreamScreenController>(tag: tag);
    } catch (e) {
      // If controller doesn't exist, show loading state instead of creating new one
      return Container(
        color: Colors.black,
        child: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(color: Colors.purple),
              SizedBox(height: 16),
              Text(
                'Initializing stream...',
                style: TextStyle(color: Colors.white),
              ),
            ],
          ),
        ),
      );
    }

    return Scaffold(
      backgroundColor: blackPure(context),
      resizeToAvoidBottomInset: false,
      body: PopScope(
        canPop: false,
        onPopInvokedWithResult: (didPop, result) {
          if (didPop) return;
          controller.onCloseAudienceBtn();
        },
        child: Stack(
          children: [
            const LiveStreamBlurBackgroundImage(),

            /// Live StreamView
            Obx(() {
              switch (controller.liveData.value.type) {
                case null:
                case LivestreamType.livestream:
                  return LivestreamView(
                    streamViews: controller.streamViews,
                    controller: controller,
                  );
                case LivestreamType.battle:
                  return BattleView(
                    isAudience: true,
                    controller: controller,
                    margin: const EdgeInsets.only(top: 100),
                  );
                case LivestreamType.dummy:
                  return LivestreamVideoPlayer(
                      controller: controller.videoPlayerController);
              }
            }),

            KeyboardAvoider(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  LiveStreamAudienceTopView(
                      isAudience: true, controller: controller),
                  LiveStreamBottomView(
                      isAudience: true, controller: controller),
                ],
              ),
            ),

            Obx(
              () {
                Livestream stream = controller.liveData.value;
                bool isBattle = stream.battleType == BattleType.waiting;
                if (isBattle) {
                  return BattleStartCountdownOverlay(
                      isHost: widget.isHost, stream: stream);
                }
                return const SizedBox();
              },
            )
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    // Controller cleanup is now handled by the parent LiveStreamViewerScreen
    // to ensure proper sequencing of logout/login during swipe transitions
    super.dispose();
  }
}
