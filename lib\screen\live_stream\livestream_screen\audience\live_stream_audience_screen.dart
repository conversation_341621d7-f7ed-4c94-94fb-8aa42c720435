import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:keyboard_avoider/keyboard_avoider.dart';
import 'package:ratulive/model/livestream/livestream.dart';
import 'package:ratulive/screen/live_stream/livestream_screen/audience/widget/livestream_audience_top_view.dart';
import 'package:ratulive/screen/live_stream/livestream_screen/livestream_screen_controller.dart';
import 'package:ratulive/screen/live_stream/livestream_screen/view/battle_view.dart';
import 'package:ratulive/screen/live_stream/livestream_screen/view/live_stream_bottom_view.dart';
import 'package:ratulive/screen/live_stream/livestream_screen/view/live_video_player.dart';
import 'package:ratulive/screen/live_stream/livestream_screen/view/livestream_view.dart';
import 'package:ratulive/screen/live_stream/livestream_screen/widget/battle_start_countdown_overlay.dart';
import 'package:ratulive/screen/live_stream/livestream_screen/widget/live_stream_background_blur_image.dart';
import 'package:ratulive/utilities/theme_res.dart';

class LiveStreamAudienceScreen extends StatefulWidget {
  final Livestream livestream;
  final bool isHost;

  const LiveStreamAudienceScreen(
      {super.key, required this.livestream, required this.isHost});

  @override
  State<LiveStreamAudienceScreen> createState() => _LiveStreamAudienceScreenState();
}

class _LiveStreamAudienceScreenState extends State<LiveStreamAudienceScreen> {

  @override
  Widget build(BuildContext context) {
    // Use a unique tag for each controller to ensure separate instances
    final controller = Get.put(
      LivestreamScreenController(widget.livestream.obs, widget.isHost),
      tag: widget.livestream.roomID,
    );

    return Scaffold(
      backgroundColor: blackPure(context),
      resizeToAvoidBottomInset: false,
      body: PopScope(
        canPop: false,
        onPopInvokedWithResult: (didPop, result) {
          if (didPop) return;
          Get.find<LivestreamScreenController>(tag: widget.livestream.roomID)
              .onCloseAudienceBtn();
        },
        child: Stack(
          children: [
            const LiveStreamBlurBackgroundImage(),

            /// Live StreamView
            Obx(() {
              switch (controller.liveData.value.type) {
                case null:
                case LivestreamType.livestream:
                  return LivestreamView(
                    streamViews: Get.find<LivestreamScreenController>(tag: widget.livestream.roomID).streamViews,
                    controller: Get.find<LivestreamScreenController>(tag: widget.livestream.roomID),
                  );
                case LivestreamType.battle:
                  return BattleView(
                    isAudience: true,
                    controller: Get.find<LivestreamScreenController>(tag: widget.livestream.roomID),
                    margin: const EdgeInsets.only(top: 100),
                  );
                case LivestreamType.dummy:
                  return LivestreamVideoPlayer(
                      controller: Get.find<LivestreamScreenController>(tag: widget.livestream.roomID).videoPlayerController);
              }
            }),

            KeyboardAvoider(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  LiveStreamAudienceTopView(
                      isAudience: true, controller: Get.find<LivestreamScreenController>(tag: widget.livestream.roomID)),
                  LiveStreamBottomView(
                      isAudience: true, controller: Get.find<LivestreamScreenController>(tag: widget.livestream.roomID)),
                ],
              ),
            ),

            Obx(
              () {
                                Livestream stream = Get.find<LivestreamScreenController>(tag: widget.livestream.roomID).liveData.value;
                bool isBattle = stream.battleType == BattleType.waiting;
                if (isBattle) {
                  return BattleStartCountdownOverlay(
                      isHost: widget.isHost, stream: stream);
                }
                return const SizedBox();
              },
            )
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    Get.delete<LivestreamScreenController>(tag: widget.livestream.roomID);
    super.dispose();
  }
}
