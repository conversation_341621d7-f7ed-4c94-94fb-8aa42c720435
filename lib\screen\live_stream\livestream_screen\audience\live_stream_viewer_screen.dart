import 'package:flutter/material.dart';
import 'package:ratulive/model/livestream/livestream.dart';
import 'package:ratulive/screen/live_stream/livestream_screen/audience/live_stream_audience_screen.dart';

class LiveStreamViewerScreen extends StatefulWidget {
  final List<Livestream> livestreams;
  final int initialIndex;

  const LiveStreamViewerScreen({
    super.key,
    required this.livestreams,
    required this.initialIndex,
  });

  @override
  State<LiveStreamViewerScreen> createState() => _LiveStreamViewerScreenState();
}

class _LiveStreamViewerScreenState extends State<LiveStreamViewerScreen> {
  late PageController _pageController;

  @override
  void initState() {
    super.initState();
    _pageController = PageController(initialPage: widget.initialIndex);
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return PageView.builder(
      controller: _pageController,
      scrollDirection: Axis.vertical,
      itemCount: widget.livestreams.length,
      itemBuilder: (context, index) {
        return LiveStreamAudienceScreen(
          livestream: widget.livestreams[index],
          isHost: false,
        );
      },
    );
  }
}
