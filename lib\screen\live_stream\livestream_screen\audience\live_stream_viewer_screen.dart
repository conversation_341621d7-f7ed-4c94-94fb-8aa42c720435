import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ratulive/model/livestream/livestream.dart';
import 'package:ratulive/screen/live_stream/livestream_screen/audience/live_stream_audience_screen.dart';
import 'package:ratulive/screen/live_stream/livestream_screen/livestream_screen_controller.dart';
import 'package:ratulive/common/manager/logger.dart';

class LiveStreamViewerScreen extends StatefulWidget {
  final List<Livestream> livestreams;
  final int initialIndex;

  const LiveStreamViewerScreen({
    super.key,
    required this.livestreams,
    required this.initialIndex,
  });

  @override
  State<LiveStreamViewerScreen> createState() => _LiveStreamViewerScreenState();
}

class _LiveStreamViewerScreenState extends State<LiveStreamViewerScreen> {
  late PageController _pageController;
  int _currentIndex = 0;
  LivestreamScreenController? _currentController;

  @override
  void initState() {
    super.initState();
    _currentIndex = widget.initialIndex;
    _pageController = PageController(initialPage: widget.initialIndex);

    // Initialize the first stream controller
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      await _initializeStreamController(_currentIndex);
    });
  }

  @override
  void dispose() {
    // Note: We can't await in dispose, but logoutRoom handles cleanup internally
    _cleanupCurrentController();
    _pageController.dispose();
    super.dispose();
  }

  Future<void> _initializeStreamController(int index) async {
    if (index < 0 || index >= widget.livestreams.length) return;

    final livestream = widget.livestreams[index];
    final tag = livestream.roomID;

    if (tag == null || tag.isEmpty) {
      Loggers.error('Invalid room ID for livestream at index $index');
      return;
    }

    // CRITICAL: Clean up previous controller and WAIT for logout to complete
    await _cleanupCurrentController();

    // Only create new controller after previous room logout is complete
    _currentController = Get.put(
      LivestreamScreenController(livestream.obs, false),
      tag: tag,
    );

    Loggers.info('Initialized controller for room: $tag');
  }

  Future<void> _cleanupCurrentController() async {
    if (_currentController != null) {
      final tag = widget.livestreams[_currentIndex].roomID;
      if (tag != null && tag.isNotEmpty) {
        try {
          // Ensure proper cleanup of the controller
          if (Get.isRegistered<LivestreamScreenController>(tag: tag)) {
            final controller = Get.find<LivestreamScreenController>(tag: tag);
            // CRITICAL: Wait for logout to complete before proceeding
            await controller.logoutRoom(2); // Logout with index 2 for swipe transition
            Get.delete<LivestreamScreenController>(tag: tag);
            Loggers.info('Cleaned up controller for room: $tag');
          }
        } catch (e) {
          Loggers.error('Error cleaning up controller: $e');
        }
      }
      _currentController = null;
    }
  }

  void _onPageChanged(int index) {
    if (index == _currentIndex) return;

    Loggers.info('Page changed from $_currentIndex to $index');

    // Update current index
    _currentIndex = index;

    // Initialize controller for new page - now properly async
    _initializeStreamController(index);
  }

  @override
  Widget build(BuildContext context) {
    return PageView.builder(
      controller: _pageController,
      scrollDirection: Axis.vertical,
      itemCount: widget.livestreams.length,
      onPageChanged: _onPageChanged,
      itemBuilder: (context, index) {
        // Only render the current page to avoid multiple simultaneous room logins
        if (index == _currentIndex) {
          return LiveStreamAudienceScreen(
            livestream: widget.livestreams[index],
            isHost: false,
            key: ValueKey(widget.livestreams[index].roomID),
          );
        } else {
          // Return a placeholder for non-current pages
          return Container(
            color: Colors.black,
            child: const Center(
              child: CircularProgressIndicator(),
            ),
          );
        }
      },
    );
  }
}
