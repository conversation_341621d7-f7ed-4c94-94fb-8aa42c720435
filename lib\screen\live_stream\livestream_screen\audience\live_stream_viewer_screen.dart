import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ratulive/model/livestream/livestream.dart';
import 'package:ratulive/screen/live_stream/livestream_screen/audience/live_stream_audience_screen.dart';
import 'package:ratulive/screen/live_stream/livestream_screen/livestream_screen_controller.dart';
import 'package:ratulive/common/manager/logger.dart';
import 'package:ratulive/utilities/stream_debug_helper.dart';

class LiveStreamViewerScreen extends StatefulWidget {
  final List<Livestream> livestreams;
  final int initialIndex;

  const LiveStreamViewerScreen({
    super.key,
    required this.livestreams,
    required this.initialIndex,
  });

  @override
  State<LiveStreamViewerScreen> createState() => _LiveStreamViewerScreenState();
}

class _LiveStreamViewerScreenState extends State<LiveStreamViewerScreen> {
  late PageController _pageController;
  int _currentIndex = 0;
  LivestreamScreenController? _currentController;
  bool _isTransitioning = false;

  // Track which controllers have been created to prevent duplicates
  final Set<String> _createdControllers = <String>{};

  @override
  void initState() {
    super.initState();
    _currentIndex = widget.initialIndex;
    _pageController = PageController(initialPage: widget.initialIndex);

    // Initialize the first stream controller
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      await _initializeStreamController(_currentIndex);
    });
  }

  @override
  void dispose() {
    // Note: We can't await in dispose, but logoutRoom handles cleanup internally
    _cleanupCurrentController();
    _pageController.dispose();
    super.dispose();
  }

  Future<void> _initializeStreamController(int index) async {
    if (index < 0 || index >= widget.livestreams.length) return;
    if (_isTransitioning) {
      Loggers.warning('Already transitioning, skipping initialization for index $index');
      return;
    }

    _isTransitioning = true;

    try {
      final livestream = widget.livestreams[index];
      final tag = livestream.roomID;

      if (tag == null || tag.isEmpty) {
        Loggers.error('Invalid room ID for livestream at index $index');
        return;
      }

      Loggers.info('🔄 Starting room transition to: $tag (index: $index)');

      // Debug: Log current state before transition
      StreamDebugHelper.instance.debugDump('Before room transition');
      StreamDebugHelper.instance.logRoomTransition(
        widget.livestreams[_currentIndex].roomID ?? 'unknown',
        tag,
        'START'
      );

      // CRITICAL: Clean up previous controller and WAIT for logout to complete
      await _cleanupCurrentController();

      // Ensure we're not creating duplicate controllers
      if (_createdControllers.contains(tag)) {
        Loggers.warning('Controller for room $tag already exists, cleaning up first');
        if (Get.isRegistered<LivestreamScreenController>(tag: tag)) {
          Get.delete<LivestreamScreenController>(tag: tag);
        }
        _createdControllers.remove(tag);
      }

      // Only create new controller after previous room logout is complete
      _currentController = Get.put(
        LivestreamScreenController(livestream.obs, false),
        tag: tag,
      );

      _createdControllers.add(tag);
      Loggers.info('✅ Successfully initialized controller for room: $tag');

      // Debug: Log state after successful initialization
      StreamDebugHelper.instance.logControllerInfo(tag);
      StreamDebugHelper.instance.logRoomTransition(
        widget.livestreams[_currentIndex].roomID ?? 'unknown',
        tag,
        'COMPLETE'
      );

    } catch (e) {
      final errorTag = widget.livestreams[index].roomID ?? 'unknown';
      Loggers.error('❌ Error initializing stream controller: $e');
      StreamDebugHelper.instance.logErrorWithContext(e.toString(), 'Controller initialization', errorTag);
    } finally {
      _isTransitioning = false;
    }
  }

  Future<void> _cleanupCurrentController() async {
    if (_currentController != null) {
      final tag = widget.livestreams[_currentIndex].roomID;
      if (tag != null && tag.isNotEmpty) {
        try {
          Loggers.info('🧹 Starting cleanup for room: $tag');

          // Ensure proper cleanup of the controller
          if (Get.isRegistered<LivestreamScreenController>(tag: tag)) {
            final controller = Get.find<LivestreamScreenController>(tag: tag);

            // CRITICAL: Wait for logout to complete before proceeding
            Loggers.info('🚪 Logging out from room: $tag');
            await controller.logoutRoom(2); // Logout with index 2 for swipe transition

            // Additional safety delay to ensure Zego engine state is clean
            await Future.delayed(const Duration(milliseconds: 300));

            Get.delete<LivestreamScreenController>(tag: tag);
            _createdControllers.remove(tag);
            Loggers.info('✅ Successfully cleaned up controller for room: $tag');
          }
        } catch (e) {
          Loggers.error('❌ Error cleaning up controller: $e');
        }
      }
      _currentController = null;
    }
  }

  void _onPageChanged(int index) {
    if (index == _currentIndex) return;
    if (_isTransitioning) {
      Loggers.warning('Already transitioning, ignoring page change to $index');
      return;
    }

    Loggers.info('📱 Page changed from $_currentIndex to $index');

    // Update current index
    final previousIndex = _currentIndex;
    _currentIndex = index;

    // Initialize controller for new page - now properly async
    _initializeStreamController(index).catchError((error) {
      Loggers.error('Failed to initialize controller for index $index: $error');
      // Revert to previous index on error
      _currentIndex = previousIndex;
    });
  }

  @override
  Widget build(BuildContext context) {
    return PageView.builder(
      controller: _pageController,
      scrollDirection: Axis.vertical,
      itemCount: widget.livestreams.length,
      onPageChanged: _onPageChanged,
      // CRITICAL: Prevent PageView from pre-building adjacent pages
      allowImplicitScrolling: false,
      itemBuilder: (context, index) {
        final livestream = widget.livestreams[index];
        final roomID = livestream.roomID ?? '';

        // Always return a widget, but only show content for current page
        return Container(
          key: ValueKey('stream_container_$roomID'),
          color: Colors.black,
          child: index == _currentIndex
            ? _buildCurrentStreamContent(livestream, index)
            : _buildPlaceholderContent(index),
        );
      },
    );
  }

  Widget _buildCurrentStreamContent(Livestream livestream, int index) {
    final tag = livestream.roomID;

    if (tag == null || tag.isEmpty) {
      return const Center(
        child: Text(
          'Invalid room ID',
          style: TextStyle(color: Colors.white),
        ),
      );
    }

    // Only show content if controller exists and is ready
    if (Get.isRegistered<LivestreamScreenController>(tag: tag) && !_isTransitioning) {
      return LiveStreamAudienceScreen(
        livestream: livestream,
        isHost: false,
        key: ValueKey('stream_$tag'),
      );
    } else {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(color: Colors.purple),
            SizedBox(height: 16),
            Text(
              'Connecting to stream...',
              style: TextStyle(color: Colors.white),
            ),
          ],
        ),
      );
    }
  }

  Widget _buildPlaceholderContent(int index) {
    return const Center(
      child: Text(
        'Swipe to view stream',
        style: TextStyle(color: Colors.white54),
      ),
    );
  }
}
