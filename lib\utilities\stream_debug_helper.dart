import 'package:get/get.dart';
import 'package:ratulive/common/manager/logger.dart';
import 'package:ratulive/screen/live_stream/livestream_screen/livestream_screen_controller.dart';

/// Debug utility to monitor live stream room states and transitions
class StreamDebugHelper {
  static final StreamDebugHelper _instance = StreamDebugHelper._internal();
  factory StreamDebugHelper() => _instance;
  StreamDebugHelper._internal();

  static StreamDebugHelper get instance => _instance;

  /// Log current state of all registered controllers
  void logAllControllerStates() {
    Loggers.info('🔍 === CONTROLLER STATE AUDIT ===');

    // Check for any registered LivestreamScreenController instances
    try {
      // We can't easily enumerate all registered controllers in GetX
      // So we'll log what we can detect
      Loggers.info('📊 GetX has registered controllers');
    } catch (e) {
      Loggers.info('📊 No GetX controllers found or error accessing them');
    }

    Loggers.info('🔍 === END CONTROLLER AUDIT ===');
  }

  /// Log room transition details
  void logRoomTransition(String fromRoom, String toRoom, String phase) {
    Loggers.info('🔄 ROOM TRANSITION [$phase]: $fromRoom → $toRoom');
  }

  /// Log video rendering state
  void logVideoRenderingState(String roomID, int streamViewCount) {
    Loggers.info('📺 VIDEO STATE - Room: $roomID, StreamViews: $streamViewCount');
  }

  /// Log error with context
  void logErrorWithContext(String error, String context, String roomID) {
    Loggers.error('💥 ERROR in $context for room $roomID: $error');
  }

  /// Check if a controller exists for a room
  bool isControllerRegistered(String roomID) {
    try {
      Get.find<LivestreamScreenController>(tag: roomID);
      return true;
    } catch (e) {
      return false;
    }
  }

  /// Get controller info for debugging
  Map<String, dynamic> getControllerInfo(String roomID) {
    try {
      final controller = Get.find<LivestreamScreenController>(tag: roomID);
      return {
        'roomID': roomID,
        'isRegistered': true,
        'streamViewsCount': controller.streamViews.length,
        'liveDataRoomID': controller.liveData.value.roomID,
        'isHost': controller.isHost,
      };
    } catch (e) {
      return {
        'roomID': roomID,
        'isRegistered': false,
        'error': e.toString(),
      };
    }
  }

  /// Log detailed controller info
  void logControllerInfo(String roomID) {
    final info = getControllerInfo(roomID);
    Loggers.info('🎮 CONTROLLER INFO: $info');
  }

  /// Monitor room state changes
  void startRoomStateMonitoring() {
    Loggers.info('🔍 Starting room state monitoring...');
    // This could be expanded to periodically check states
  }

  /// Log memory usage and cleanup status
  void logMemoryStatus() {
    Loggers.info('💾 === MEMORY STATUS ===');
    // Could add memory usage tracking here
    Loggers.info('💾 === END MEMORY STATUS ===');
  }

  /// Comprehensive debug dump
  void debugDump(String context) {
    Loggers.info('🚨 === DEBUG DUMP: $context ===');
    logAllControllerStates();
    logMemoryStatus();
    Loggers.info('🚨 === END DEBUG DUMP ===');
  }

  /// Validate room transition safety
  bool validateRoomTransitionSafety(String fromRoom, String toRoom) {
    Loggers.info('🔒 Validating room transition safety: $fromRoom → $toRoom');
    
    // Check if source room controller exists
    bool fromExists = isControllerRegistered(fromRoom);
    bool toExists = isControllerRegistered(toRoom);
    
    if (fromExists && toExists) {
      Loggers.warning('⚠️ Both controllers exist - potential multiple rooms issue!');
      return false;
    }
    
    if (!fromExists && !toExists) {
      Loggers.info('✅ No controllers exist - safe to proceed');
      return true;
    }
    
    if (fromExists && !toExists) {
      Loggers.info('✅ Only source exists - need to cleanup first');
      return true;
    }
    
    if (!fromExists && toExists) {
      Loggers.warning('⚠️ Only target exists - unexpected state');
      return false;
    }
    
    return true;
  }
}
