# Live Stream Room Management Fix

## Problem: "Cannot join multiple rooms simultaneously"

The error occurs because the app was trying to join multiple Zego rooms at the same time during swipe transitions, violating the single-room constraint.

## Root Cause Analysis

### Before Fix:
```
User swipes to new stream
    ↓
PageView creates new LiveStreamAudienceScreen
    ↓
New controller created immediately
    ↓
New controller calls loginRoom() in onInit()
    ↓
❌ BOTH old and new rooms are active simultaneously
    ↓
ERROR: "Cannot join multiple rooms simultaneously"
```

### The Problem Areas:
1. **Concurrent Controller Creation**: PageView.builder created all widgets simultaneously
2. **No Logout Sequencing**: Previous room wasn't properly logged out before joining new room
3. **Race Conditions**: Async operations weren't properly awaited
4. **Missing Error Recovery**: No handling for multiple room errors

## Solution Implemented

### After Fix:
```
User swipes to new stream
    ↓
_onPageChanged() triggered
    ↓
_initializeStreamController() called
    ↓
await _cleanupCurrentController() - WAITS for logout
    ↓
Previous room logout completes + 200ms delay
    ↓
New controller created and loginRoom() called
    ↓
✅ Only ONE room active at any time
    ↓
SUCCESS: Smooth stream transition
```

## Key Changes Made

### 1. Sequential Room Management
```dart
// OLD: Concurrent execution
void _cleanupCurrentController() {
    controller.logoutRoom(2); // ❌ Not awaited
    Get.delete<LivestreamScreenController>(tag: tag);
}

// NEW: Sequential execution
Future<void> _cleanupCurrentController() async {
    await controller.logoutRoom(2); // ✅ Properly awaited
    Get.delete<LivestreamScreenController>(tag: tag);
}
```

### 2. Proper Async Flow
```dart
// OLD: Race condition possible
void _initializeStreamController(int index) {
    _cleanupCurrentController(); // ❌ Not awaited
    _currentController = Get.put(...); // ❌ Immediate creation
}

// NEW: Proper sequencing
Future<void> _initializeStreamController(int index) async {
    await _cleanupCurrentController(); // ✅ Wait for cleanup
    _currentController = Get.put(...); // ✅ Create after cleanup
}
```

### 3. Enhanced Logout Process
```dart
Future<void> logoutRoom(int index) async {
    // ... cleanup code ...
    if (roomID.isNotEmpty) {
        await zegoEngine.logoutRoom(roomID);
        // ✅ Additional delay to ensure clean state
        await Future.delayed(const Duration(milliseconds: 200));
        Loggers.info('Successfully logged out from room: $roomID');
    }
}
```

### 4. Error Recovery Mechanism
```dart
// Special handling for multiple rooms error (1002001)
if (result.errorCode == 1002001) {
    Loggers.warning('Multiple rooms error detected - attempting recovery');
    try {
        // Force logout from any existing room
        await zegoEngine.logoutRoom('');
        await Future.delayed(const Duration(milliseconds: 500));
        
        // Retry login once
        final retryResult = await zegoEngine.loginRoom(roomID, user, config: roomConfig);
        if (retryResult.errorCode == 0) {
            Loggers.success('Recovery successful');
            return retryResult;
        }
    } catch (e) {
        Loggers.error('Recovery attempt failed: $e');
    }
}
```

## Flow Diagram

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   User Swipes   │───▶│  Page Changed   │───▶│ Start Cleanup   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                        │
                                                        ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ Create New      │◀───│ Wait for Clean  │◀───│ Logout Previous │
│ Controller      │    │ State (200ms)   │    │ Room (Async)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │
         ▼
┌─────────────────┐    ┌─────────────────┐
│ Login to New    │───▶│ Success! Single │
│ Room (Async)    │    │ Room Active     │
└─────────────────┘    └─────────────────┘
```

## Testing the Fix

### Test Scenarios:
1. **Normal Swiping**: Swipe between 3-4 different live streams rapidly
2. **Network Issues**: Test during poor connectivity
3. **Rapid Swiping**: Very fast consecutive swipes
4. **Edge Cases**: Swipe when stream ends, invalid room IDs
5. **Recovery**: Simulate multiple room error and verify recovery

### Expected Results:
- ✅ No "Cannot join multiple rooms simultaneously" errors
- ✅ Smooth transitions between streams
- ✅ Proper cleanup of resources
- ✅ Error recovery when issues occur
- ✅ Single active room at all times

## Monitoring and Debugging

### Log Messages to Watch:
```
INFO: Page changed from 0 to 1
INFO: Logging out from room: 12345 (index: 2)
INFO: Successfully logged out from room: 12345
INFO: Cleaned up controller for room: 12345
INFO: Initialized controller for room: 67890
INFO: Attempting to login to room: 67890
SUCCESS: Successfully logged into room: 67890
```

### Error Recovery Logs:
```
WARNING: Multiple rooms error detected - attempting recovery
INFO: Recovery successful - logged into room: 67890
```

This fix ensures the **LEAVE → WAIT → JOIN** sequence is properly followed, eliminating the multiple rooms error and providing a smooth user experience during live stream swiping.
